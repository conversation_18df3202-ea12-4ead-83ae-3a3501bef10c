//
//  ContentView.swift
//  demox
//
//  Created by ltt on 2025/8/31.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedTab = "All"
    @State private var selectedBottomTab = "Home"

    let tabs = ["All", "Music", "Podcasts", "Audiobooks"]

    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()

                VStack(spacing: 0) {
                    // Top tab bar
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(tabs, id: \.self) { tab in
                                Button(action: {
                                    selectedTab = tab
                                }) {
                                    Text(tab)
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(selectedTab == tab ? .black : .white)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(
                                            RoundedRectangle(cornerRadius: 20)
                                                .fill(selectedTab == tab ? .green : Color.gray.opacity(0.3))
                                        )
                                }
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                    .padding(.top, 10)

                    // Main content
                    ScrollView {
                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 12) {
                            // Content cards
                            ContentCard(
                                title: "Brat and it's completely diff...",
                                subtitle: "",
                                imageName: "music.note",
                                backgroundColor: .green
                            )

                            ContentCard(
                                title: "Wicked Official Playlist",
                                subtitle: "",
                                imageName: "music.note",
                                backgroundColor: .orange
                            )

                            ContentCard(
                                title: "Gracie <PERSON>",
                                subtitle: "",
                                imageName: "person.circle.fill",
                                backgroundColor: .clear,
                                hasImage: true
                            )

                            ContentCard(
                                title: "More Life",
                                subtitle: "",
                                imageName: "music.note",
                                backgroundColor: .brown
                            )

                            ContentCard(
                                title: "DJ",
                                subtitle: "",
                                imageName: "waveform.circle.fill",
                                backgroundColor: .blue
                            )

                            ContentCard(
                                title: "Today's Top Hits",
                                subtitle: "",
                                imageName: "music.note",
                                backgroundColor: .gray
                            )

                            ContentCard(
                                title: "eternal sunshine",
                                subtitle: "",
                                imageName: "sun.max.fill",
                                backgroundColor: .orange
                            )

                            ContentCard(
                                title: "Short'n'Sweet",
                                subtitle: "",
                                imageName: "heart.fill",
                                backgroundColor: .pink
                            )
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)

                        // Picked for you section
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Picked for you")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 20)

                            PodcastCard()
                        }
                        .padding(.top, 30)

                        // New releases section
                        VStack(alignment: .leading, spacing: 16) {
                            Text("New releases for you")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 20)

                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(0..<3) { index in
                                        AlbumCard(index: index)
                                    }
                                }
                                .padding(.horizontal, 20)
                            }
                        }
                        .padding(.top, 30)
                        .padding(.bottom, 100)
                    }
                }
            }
        }
        .overlay(
            // Bottom navigation
            VStack {
                Spacer()
                BottomNavigationView(selectedTab: $selectedBottomTab)
            }
        )
    }
}

// Content Card Component
struct ContentCard: View {
    let title: String
    let subtitle: String
    let imageName: String
    let backgroundColor: Color
    var hasImage: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                if hasImage {
                    Image(systemName: "person.crop.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.white)
                } else {
                    Image(systemName: imageName)
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                }
                Spacer()
            }

            Text(title)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
        }
        .padding(12)
        .frame(height: 80)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(backgroundColor.opacity(0.8))
        )
    }
}

// Podcast Card Component
struct PodcastCard: View {
    var body: some View {
        HStack(spacing: 12) {
            // Podcast image
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.pink)
                .frame(width: 120, height: 120)
                .overlay(
                    VStack {
                        Image(systemName: "mouth.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                        Text("Sounds\nLike A\nCult")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                    }
                )

            VStack(alignment: .leading, spacing: 8) {
                Text("Podcast")
                    .font(.caption)
                    .foregroundColor(.gray)

                Text("Sounds Like A Cult")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)

                Text("A podcast about the modern-day \"cults\" we all follow.")
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
                    .lineLimit(3)

                Spacer()
            }

            Spacer()

            Button(action: {}) {
                Image(systemName: "ellipsis")
                    .font(.system(size: 16))
                    .foregroundColor(.gray)
            }
        }
        .padding(.horizontal, 20)
    }
}

// Album Card Component
struct AlbumCard: View {
    let index: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            RoundedRectangle(cornerRadius: 8)
                .fill(albumColor)
                .frame(width: 140, height: 140)
                .overlay(
                    Image(systemName: albumIcon)
                        .font(.system(size: 40))
                        .foregroundColor(.white)
                )

            Text(albumTitle)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
                .lineLimit(2)
        }
    }

    private var albumColor: Color {
        switch index {
        case 0: return .yellow
        case 1: return .blue
        default: return .brown
        }
    }

    private var albumIcon: String {
        switch index {
        case 0: return "gamecontroller.fill"
        case 1: return "music.note"
        default: return "photo.fill"
        }
    }

    private var albumTitle: String {
        switch index {
        case 0: return "Game Album"
        case 1: return "DISCLOSURE PA SALTED"
        default: return "Photo Collection"
        }
    }
}

// Bottom Navigation Component
struct BottomNavigationView: View {
    @Binding var selectedTab: String

    let tabs = [
        ("Home", "house.fill"),
        ("Search", "magnifyingglass"),
        ("Your Library", "books.vertical.fill"),
        ("Create", "plus")
    ]

    var body: some View {
        HStack {
            ForEach(tabs, id: \.0) { tab in
                Button(action: {
                    selectedTab = tab.0
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.1)
                            .font(.system(size: 20))
                            .foregroundColor(selectedTab == tab.0 ? .white : .gray)

                        Text(tab.0)
                            .font(.system(size: 10))
                            .foregroundColor(selectedTab == tab.0 ? .white : .gray)
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(
            Color.black.opacity(0.9)
                .blur(radius: 10)
        )
    }
}

#Preview {
    ContentView()
}
