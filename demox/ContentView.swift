//
//  ContentView.swift
//  demox
//
//  Created by ltt on 2025/8/31.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedTab = "All"
    @State private var selectedBottomTab = "Home"

    var body: some View {
        NavigationView {
            DSContainer(backgroundColor: DesignTokens.Colors.background) {
                VStack(spacing: 0) {
                    // Top tab bar using design system
                    DSTabBar.topTabs(
                        tabs: ["All", "Music", "Podcasts", "Audiobooks"],
                        selectedTab: $selectedTab
                    )

                    // Main content
                    ScrollView {
                        VStack(spacing: 0) {
                            // Content cards grid
                            DSSection {
                                DSGrid(columns: 2) {
                                    DSCard(
                                        title: "Brat and it's completely diff...",
                                        imageName: "music.note",
                                        backgroundColor: DesignTokens.Colors.primary
                                    )

                                    DSCard(
                                        title: "Wicked Official Playlist",
                                        imageName: "music.note",
                                        backgroundColor: DesignTokens.Colors.contentOrange
                                    )

                                    DSCard(
                                        title: "<PERSON> <PERSON>",
                                        imageName: "person.circle.fill",
                                        backgroundColor: DesignTokens.Colors.surfaceVariant
                                    )

                                    DSCard(
                                        title: "More Life",
                                        imageName: "music.note",
                                        backgroundColor: DesignTokens.Colors.contentBrown
                                    )

                                    DSCard(
                                        title: "DJ",
                                        imageName: "waveform.circle.fill",
                                        backgroundColor: DesignTokens.Colors.contentBlue
                                    )

                                    DSCard(
                                        title: "Today's Top Hits",
                                        imageName: "music.note",
                                        backgroundColor: DesignTokens.Colors.contentGray
                                    )

                                    DSCard(
                                        title: "eternal sunshine",
                                        imageName: "sun.max.fill",
                                        backgroundColor: DesignTokens.Colors.contentOrange
                                    )

                                    DSCard(
                                        title: "Short'n'Sweet",
                                        imageName: "heart.fill",
                                        backgroundColor: DesignTokens.Colors.contentPink
                                    )
                                }
                                .padding(.horizontal, DesignTokens.Spacing.contentPadding)
                            }

                            // Picked for you section
                            DSSection(title: "Picked for you") {
                                DSCard(
                                    type: .podcast,
                                    title: "Sounds Like A Cult",
                                    subtitle: "A podcast about the modern-day \"cults\" we all follow.",
                                    description: "Podcast",
                                    imageName: "mouth.fill",
                                    backgroundColor: DesignTokens.Colors.contentPink
                                )
                            }

                            // New releases section
                            DSSection(title: "New releases for you") {
                                DSHorizontalScroll {
                                    ForEach(0..<3, id: \.self) { index in
                                        DSCard(
                                            type: .album,
                                            title: albumTitles[index],
                                            imageName: albumIcons[index],
                                            backgroundColor: albumColors[index]
                                        )
                                    }
                                }
                            }

                            DSSpacer(height: 100)
                        }
                    }
                }
            }
        }
        .overlay(
            // Bottom navigation using design system
            VStack {
                Spacer()
                DSTabBar.bottomNavigation(selectedTab: $selectedBottomTab)
            }
        )
    }

    // MARK: - Data
    private let albumTitles = ["Game Album", "DISCLOSURE PA SALTED", "Photo Collection"]
    private let albumIcons = ["gamecontroller.fill", "music.note", "photo.fill"]
    private let albumColors = [DesignTokens.Colors.contentYellow, DesignTokens.Colors.contentBlue, DesignTokens.Colors.contentBrown]
}



#Preview {
    ContentView()
}
