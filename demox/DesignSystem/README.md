# Music App Design System

一套完整的SwiftUI设计系统，专为音乐应用打造，遵循DRY原则和现代设计规范。

## 🎯 设计原则

- **一致性**: 统一的视觉语言和交互模式
- **可访问性**: 考虑无障碍设计的组件
- **可扩展性**: 易于扩展和维护的架构
- **性能优化**: 流畅的用户体验
- **深色主题**: 专为深色模式界面设计

## 📁 目录结构

```
DesignSystem/
├── DesignTokens.swift          # 设计令牌（颜色、字体、间距等）
├── Components/
│   └── UI/
│       ├── Button/
│       │   └── DSButton.swift  # 按钮组件
│       ├── Card/
│       │   └── DSCard.swift    # 卡片组件
│       ├── Icon/
│       │   └── DSIcon.swift    # 图标组件
│       ├── Navigation/
│       │   └── DSTabBar.swift  # 导航组件
│       ├── Text/
│       │   └── DSText.swift    # 文本组件
│       ├── Layout/
│       │   └── DSLayout.swift  # 布局组件
│       └── DSComponents.swift  # 组件索引
└── Playground/
    └── DSPlayground.swift      # 组件预览页面
```

## 🎨 设计令牌 (Design Tokens)

### 颜色系统
- **主色调**: 绿色系 (`DesignTokens.Colors.primary`)
- **背景色**: 黑色系 (`DesignTokens.Colors.background`)
- **表面色**: 灰色系 (`DesignTokens.Colors.surface`)
- **内容色**: 多彩色系 (蓝、橙、粉、黄、棕、灰)

### 字体系统
- **标题**: H1, H2, H3 (粗体)
- **正文**: Body Large, Medium, Small
- **标签**: Label Large, Medium, Small
- **说明**: Caption

### 间距系统
- **XS**: 4pt
- **SM**: 8pt  
- **MD**: 12pt
- **LG**: 16pt
- **XL**: 20pt
- **XXL**: 24pt
- **XXXL**: 32pt

### 圆角系统
- **XS**: 4pt
- **SM**: 8pt (卡片)
- **MD**: 12pt
- **LG**: 16pt (按钮)
- **XL**: 20pt
- **Pill**: 50pt (标签页)

## 🧩 组件库

### 1. 按钮组件 (DSButton)
```swift
DSButton("Primary", style: .primary) { }
DSButton("Tab", style: .tab, isSelected: true) { }
```

**样式类型**:
- `.primary` - 主要按钮
- `.secondary` - 次要按钮  
- `.ghost` - 幽灵按钮
- `.tab` - 标签按钮

### 2. 卡片组件 (DSCard)
```swift
DSCard(
    type: .content,
    title: "音乐标题",
    imageName: "music.note",
    backgroundColor: DesignTokens.Colors.contentBlue
)
```

**卡片类型**:
- `.content` - 内容卡片
- `.album` - 专辑卡片
- `.podcast` - 播客卡片

### 3. 图标组件 (DSIcon)
```swift
DSIcon.music(size: .large)
DSIcon(name: "heart.fill", size: .medium, color: .red)
```

**预设图标**:
- 音乐类: `music`, `waveform`, `sun`, `heart`
- 导航类: `home`, `search`, `library`, `create`
- 用户类: `person`, `more`

### 4. 导航组件 (DSTabBar)
```swift
// 顶部标签栏
DSTabBar.topTabs(
    tabs: ["All", "Music", "Podcasts"],
    selectedTab: $selectedTab
)

// 底部导航栏
DSTabBar.bottomNavigation(selectedTab: $selectedTab)
```

### 5. 文本组件 (DSText)
```swift
DSText.heading1("大标题")
DSText.body("正文内容", color: .onBackground)
DSText.caption("说明文字", color: .onSurfaceVariant)
```

### 6. 布局组件
```swift
// 容器
DSContainer { content }

// 网格布局
DSGrid(columns: 2) { content }

// 章节布局
DSSection(title: "章节标题") { content }

// 水平滚动
DSHorizontalScroll { content }
```

## 🎮 Playground 使用

在应用中切换到"Design System"标签页，可以查看所有组件的实时预览：

1. **设计令牌** - 颜色、字体、间距展示
2. **按钮** - 各种按钮样式和状态
3. **卡片** - 不同类型的卡片组件
4. **图标** - 图标库和尺寸展示
5. **导航** - 导航组件演示
6. **文本** - 文本样式和颜色
7. **布局** - 布局组件使用示例

## 🚀 快速开始

1. 导入设计系统：
```swift
import SwiftUI
// 所有组件自动可用
```

2. 使用设计令牌：
```swift
.foregroundColor(DesignTokens.Colors.onBackground)
.font(DesignTokens.Typography.h2)
.padding(DesignTokens.Spacing.lg)
```

3. 使用组件：
```swift
DSButton("点击我", style: .primary) {
    // 处理点击
}
```

## 📝 最佳实践

1. **始终使用设计令牌**而不是硬编码值
2. **优先使用预设组件**而不是自定义实现
3. **保持组件的单一职责**原则
4. **使用语义化的颜色名称**
5. **遵循无障碍设计**指南

## 🔄 版本信息

- **版本**: 1.0.0
- **最后更新**: 2025-08-31
- **兼容性**: iOS 15.0+, SwiftUI 3.0+

## 📈 扩展指南

添加新组件时：

1. 在对应目录创建组件文件
2. 遵循 `DS` 前缀命名规范
3. 使用设计令牌而非硬编码值
4. 添加到 `DSComponents.swift` 索引
5. 在 Playground 中添加预览
6. 更新此文档

---

通过这套设计系统，我们实现了：
- ✅ **DRY原则** - 消除重复代码
- ✅ **统一设计** - 一致的视觉风格
- ✅ **易于维护** - 集中管理样式
- ✅ **快速开发** - 预制组件库
- ✅ **可视化预览** - Playground展示
