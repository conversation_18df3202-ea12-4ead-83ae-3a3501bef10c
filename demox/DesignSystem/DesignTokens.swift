//
//  DesignTokens.swift
//  demox
//
//  Created by Design System on 2025/8/31.
//

import SwiftUI

// MARK: - Design Tokens
struct DesignTokens {
    
    // MARK: - Colors
    struct Colors {
        // Primary Colors
        static let primary = Color.green
        static let primaryVariant = Color.green.opacity(0.8)
        
        // Background Colors
        static let background = Color.black
        static let surface = Color.gray.opacity(0.1)
        static let surfaceVariant = Color.gray.opacity(0.3)
        
        // Text Colors
        static let onPrimary = Color.black
        static let onBackground = Color.white
        static let onSurface = Color.white
        static let onSurfaceVariant = Color.gray
        static let secondary = Color.gray
        
        // Accent Colors
        static let accent = Color.green
        static let error = Color.red
        static let warning = Color.orange
        static let success = Color.green
        static let info = Color.blue
        
        // Content Colors
        static let contentBlue = Color.blue
        static let contentOrange = Color.orange
        static let contentPink = Color.pink
        static let contentYellow = Color.yellow
        static let contentBrown = Color.brown
        static let contentGray = Color.gray
    }
    
    // MARK: - Typography
    struct Typography {
        // Font Sizes
        static let largeTitle: CGFloat = 34
        static let title1: CGFloat = 28
        static let title2: CGFloat = 22
        static let title3: CGFloat = 20
        static let headline: CGFloat = 17
        static let body: CGFloat = 17
        static let callout: CGFloat = 16
        static let subheadline: CGFloat = 15
        static let footnote: CGFloat = 13
        static let caption1: CGFloat = 12
        static let caption2: CGFloat = 11
        
        // Font Weights
        static let light = Font.Weight.light
        static let regular = Font.Weight.regular
        static let medium = Font.Weight.medium
        static let semibold = Font.Weight.semibold
        static let bold = Font.Weight.bold
        
        // Text Styles
        static let h1 = Font.system(size: title1, weight: bold)
        static let h2 = Font.system(size: title2, weight: bold)
        static let h3 = Font.system(size: title3, weight: semibold)
        static let bodyLarge = Font.system(size: body, weight: regular)
        static let bodyMedium = Font.system(size: callout, weight: regular)
        static let bodySmall = Font.system(size: subheadline, weight: regular)
        static let labelLarge = Font.system(size: callout, weight: medium)
        static let labelMedium = Font.system(size: subheadline, weight: medium)
        static let labelSmall = Font.system(size: footnote, weight: medium)
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        static let xxxl: CGFloat = 32
        
        // Semantic Spacing
        static let cardPadding = md
        static let sectionSpacing = xxxl
        static let itemSpacing = md
        static let contentPadding = xl
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let pill: CGFloat = 50
        
        // Semantic Radius
        static let card = sm
        static let button = xl
        static let tab = pill
    }
    
    // MARK: - Shadows
    struct Shadows {
        static let small = Shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        static let medium = Shadow(color: .black.opacity(0.15), radius: 4, x: 0, y: 2)
        static let large = Shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Dimensions
    struct Dimensions {
        // Card Sizes
        static let cardHeight: CGFloat = 80
        static let albumCardSize: CGFloat = 140
        static let podcastImageSize: CGFloat = 120
        
        // Icon Sizes
        static let iconSmall: CGFloat = 16
        static let iconMedium: CGFloat = 20
        static let iconLarge: CGFloat = 24
        static let iconXLarge: CGFloat = 40
        
        // Navigation
        static let tabBarHeight: CGFloat = 60
        static let navigationHeight: CGFloat = 44
    }
}

// MARK: - Shadow Helper
struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}
