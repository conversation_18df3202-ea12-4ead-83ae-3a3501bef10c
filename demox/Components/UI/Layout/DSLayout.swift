//
//  DSLayout.swift
//  demox
//
//  Created by Design System on 2025/8/31.
//

import SwiftUI

// MARK: - DS Container
struct DSContainer<Content: View>: View {
    let content: Content
    let padding: CGFloat
    let backgroundColor: Color
    
    init(
        padding: CGFloat = DesignTokens.Spacing.contentPadding,
        backgroundColor: Color = DesignTokens.Colors.background,
        @ViewBuilder content: () -> Content
    ) {
        self.padding = padding
        self.backgroundColor = backgroundColor
        self.content = content()
    }
    
    var body: some View {
        content
            .padding(padding)
            .background(backgroundColor.ignoresSafeArea())
    }
}

// MARK: - DS Grid
struct DSGrid<Content: View>: View {
    let columns: Int
    let spacing: CGFloat
    let content: Content
    
    init(
        columns: Int = 2,
        spacing: CGFloat = DesignTokens.Spacing.itemSpacing,
        @ViewBuilder content: () -> Content
    ) {
        self.columns = columns
        self.spacing = spacing
        self.content = content()
    }
    
    var body: some View {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible()), count: columns),
            spacing: spacing
        ) {
            content
        }
    }
}

// MARK: - DS Section
struct DSSection<Content: View>: View {
    let title: String?
    let subtitle: String?
    let spacing: CGFloat
    let content: Content
    let seeAllAction: (() -> Void)?
    
    init(
        title: String? = nil,
        subtitle: String? = nil,
        spacing: CGFloat = DesignTokens.Spacing.lg,
        seeAllAction: (() -> Void)? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.subtitle = subtitle
        self.spacing = spacing
        self.seeAllAction = seeAllAction
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: spacing) {
            if let title = title {
                HStack {
                    VStack(alignment: .leading, spacing: DesignTokens.Spacing.xs) {
                        DSText.heading2(title)

                        if let subtitle = subtitle {
                            DSText.caption(subtitle)
                        }
                    }

                    Spacer()

                    if let action = seeAllAction {
                        Button(action: action) {
                            DSText.label("See All", color: .accent, size: .labelSmall)
                        }
                    }
                }
                .padding(.horizontal, DesignTokens.Spacing.contentPadding)
            }
            
            content
        }
        .padding(.top, DesignTokens.Spacing.sectionSpacing)
    }
}

// MARK: - DS Horizontal Scroll
struct DSHorizontalScroll<Content: View>: View {
    let spacing: CGFloat
    let showsIndicators: Bool
    let content: Content
    
    init(
        spacing: CGFloat = DesignTokens.Spacing.itemSpacing,
        showsIndicators: Bool = false,
        @ViewBuilder content: () -> Content
    ) {
        self.spacing = spacing
        self.showsIndicators = showsIndicators
        self.content = content()
    }
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: showsIndicators) {
            HStack(spacing: spacing) {
                content
            }
            .padding(.horizontal, DesignTokens.Spacing.contentPadding)
        }
    }
}

// MARK: - DS Spacer
struct DSSpacer: View {
    let height: CGFloat?
    let width: CGFloat?
    
    init(height: CGFloat? = nil, width: CGFloat? = nil) {
        self.height = height
        self.width = width
    }
    
    var body: some View {
        if let height = height, let width = width {
            Spacer()
                .frame(width: width, height: height)
        } else if let height = height {
            Spacer()
                .frame(height: height)
        } else if let width = width {
            Spacer()
                .frame(width: width)
        } else {
            Spacer()
        }
    }
}

// MARK: - DS Divider
struct DSDivider: View {
    let color: Color
    let thickness: CGFloat
    
    init(
        color: Color = DesignTokens.Colors.surfaceVariant,
        thickness: CGFloat = 1
    ) {
        self.color = color
        self.thickness = thickness
    }
    
    var body: some View {
        Rectangle()
            .fill(color)
            .frame(height: thickness)
    }
}

// MARK: - Preview
#Preview {
    DSContainer {
        ScrollView {
            VStack(spacing: 0) {
                DSSection(title: "Grid Example") {
                    DSGrid(columns: 2) {
                        ForEach(0..<4, id: \.self) { index in
                            DSCard(
                                title: "Item \(index + 1)",
                                imageName: "music.note",
                                backgroundColor: DesignTokens.Colors.contentBlue
                            )
                        }
                    }
                    .padding(.horizontal, DesignTokens.Spacing.contentPadding)
                }
                
                DSSection(title: "Horizontal Scroll Example") {
                    DSHorizontalScroll {
                        ForEach(0..<5, id: \.self) { index in
                            DSCard(
                                type: .album,
                                title: "Album \(index + 1)",
                                imageName: "music.note",
                                backgroundColor: DesignTokens.Colors.contentOrange
                            )
                        }
                    }
                }
                
                DSSpacer(height: 100)
            }
        }
    }
}
