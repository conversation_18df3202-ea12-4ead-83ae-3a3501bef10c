//
//  DSButton.swift
//  demox
//
//  Created by Design System on 2025/8/31.
//

import SwiftUI

// MARK: - <PERSON><PERSON> Styles
enum DSButtonStyle {
    case primary
    case secondary
    case ghost
    case tab
}

enum DSButtonSize {
    case small
    case medium
    case large
}

// MARK: - DS Button Component
struct DSButton: View {
    let title: String
    let style: DSButtonStyle
    let size: DSButtonSize
    let isSelected: Bool
    let action: () -> Void
    
    init(
        _ title: String,
        style: DSButtonStyle = .primary,
        size: DSButtonSize = .medium,
        isSelected: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.style = style
        self.size = size
        self.isSelected = isSelected
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(textFont)
                .foregroundColor(textColor)
                .padding(.horizontal, horizontalPadding)
                .padding(.vertical, verticalPadding)
                .background(
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(backgroundColor)
                )
        }
    }
    
    // MARK: - Computed Properties
    private var textFont: Font {
        switch size {
        case .small:
            return DesignTokens.Typography.labelSmall
        case .medium:
            return DesignTokens.Typography.labelMedium
        case .large:
            return DesignTokens.Typography.labelLarge
        }
    }
    
    private var textColor: Color {
        switch style {
        case .primary:
            return isSelected ? DesignTokens.Colors.onPrimary : DesignTokens.Colors.onBackground
        case .secondary:
            return DesignTokens.Colors.onSurface
        case .ghost:
            return isSelected ? DesignTokens.Colors.primary : DesignTokens.Colors.onSurfaceVariant
        case .tab:
            return isSelected ? DesignTokens.Colors.onPrimary : DesignTokens.Colors.onBackground
        }
    }
    
    private var backgroundColor: Color {
        switch style {
        case .primary:
            return isSelected ? DesignTokens.Colors.primary : DesignTokens.Colors.surfaceVariant
        case .secondary:
            return DesignTokens.Colors.surface
        case .ghost:
            return Color.clear
        case .tab:
            return isSelected ? DesignTokens.Colors.primary : DesignTokens.Colors.surfaceVariant
        }
    }
    
    private var horizontalPadding: CGFloat {
        switch size {
        case .small:
            return DesignTokens.Spacing.md
        case .medium:
            return DesignTokens.Spacing.lg
        case .large:
            return DesignTokens.Spacing.xl
        }
    }
    
    private var verticalPadding: CGFloat {
        switch size {
        case .small:
            return DesignTokens.Spacing.xs
        case .medium:
            return DesignTokens.Spacing.sm
        case .large:
            return DesignTokens.Spacing.md
        }
    }
    
    private var cornerRadius: CGFloat {
        switch style {
        case .tab:
            return DesignTokens.CornerRadius.tab
        default:
            return DesignTokens.CornerRadius.button
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        DSButton("Primary", style: .primary) {}
        DSButton("Primary Selected", style: .primary, isSelected: true) {}
        DSButton("Secondary", style: .secondary) {}
        DSButton("Ghost", style: .ghost) {}
        DSButton("Tab", style: .tab) {}
        DSButton("Tab Selected", style: .tab, isSelected: true) {}
    }
    .padding()
    .background(DesignTokens.Colors.background)
}
