//
//  DSIcon.swift
//  demox
//
//  Created by Design System on 2025/8/31.
//

import SwiftUI

// MARK: - Icon Sizes
enum DSIconSize {
    case small
    case medium
    case large
    case xLarge
    
    var value: CGFloat {
        switch self {
        case .small:
            return DesignTokens.Dimensions.iconSmall
        case .medium:
            return DesignTokens.Dimensions.iconMedium
        case .large:
            return DesignTokens.Dimensions.iconLarge
        case .xLarge:
            return DesignTokens.Dimensions.iconXLarge
        }
    }
}

// MARK: - DS Icon Component
struct DSIcon: View {
    let name: String
    let size: DSIconSize
    let color: Color
    
    init(
        name: String,
        size: DSIconSize = .medium,
        color: Color = DesignTokens.Colors.onSurface
    ) {
        self.name = name
        self.size = size
        self.color = color
    }
    
    var body: some View {
        Image(systemName: name)
            .font(.system(size: size.value))
            .foregroundColor(color)
    }
}

// MARK: - Common Icons
extension DSIcon {
    static func music(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "music.note", size: size, color: color)
    }
    
    static func person(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "person.circle.fill", size: size, color: color)
    }
    
    static func waveform(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "waveform.circle.fill", size: size, color: color)
    }
    
    static func sun(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "sun.max.fill", size: size, color: color)
    }
    
    static func heart(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "heart.fill", size: size, color: color)
    }
    
    static func home(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "house.fill", size: size, color: color)
    }
    
    static func search(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "magnifyingglass", size: size, color: color)
    }
    
    static func library(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "books.vertical.fill", size: size, color: color)
    }
    
    static func create(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "plus", size: size, color: color)
    }
    
    static func more(size: DSIconSize = .medium, color: Color = DesignTokens.Colors.onSurface) -> DSIcon {
        DSIcon(name: "ellipsis", size: size, color: color)
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        HStack(spacing: 20) {
            DSIcon.music(size: .small)
            DSIcon.music(size: .medium)
            DSIcon.music(size: .large)
            DSIcon.music(size: .xLarge)
        }
        
        HStack(spacing: 20) {
            DSIcon.person()
            DSIcon.waveform()
            DSIcon.sun()
            DSIcon.heart()
        }
        
        HStack(spacing: 20) {
            DSIcon.home()
            DSIcon.search()
            DSIcon.library()
            DSIcon.create()
        }
    }
    .padding()
    .background(DesignTokens.Colors.background)
}
