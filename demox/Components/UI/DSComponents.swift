//
//  DSComponents.swift
//  demox
//
//  Created by Design System on 2025/8/31.
//

import SwiftUI

// MARK: - Design System Components Index
// This file serves as a central import point for all design system components

// MARK: - Re-exports
// Design Tokens
public typealias Tokens = DesignTokens

// Components
public typealias Button = DSButton
public typealias Card = DSCard
public typealias Icon = DSIcon
public typealias TabBar = DSTabBar
public typealias Text = DSText
public typealias SectionHeader = DSSectionHeader

// Layout
public typealias Container = DSContainer
public typealias Grid = DSGrid
public typealias Section = DSSection
public typealias HorizontalScroll = DSHorizontalScroll
public typealias Spacer = DSSpacer
public typealias Divider = DSDivider

// MARK: - Design System Theme
struct DSTheme {
    static func apply() {
        // Apply global theme settings if needed
        // This could include setting up appearance proxies, etc.
    }
}

// MARK: - Component Categories for Playground
enum DSComponentCategory: String, CaseIterable {
    case tokens = "Design Tokens"
    case buttons = "Buttons"
    case cards = "Cards"
    case icons = "Icons"
    case navigation = "Navigation"
    case text = "Text & Typography"
    case layout = "Layout"
    
    var components: [String] {
        switch self {
        case .tokens:
            return ["Colors", "Typography", "Spacing", "Corner Radius", "Shadows"]
        case .buttons:
            return ["Primary Button", "Secondary Button", "Ghost Button", "Tab Button"]
        case .cards:
            return ["Content Card", "Album Card", "Podcast Card"]
        case .icons:
            return ["System Icons", "Music Icons", "Navigation Icons"]
        case .navigation:
            return ["Top Tab Bar", "Bottom Navigation"]
        case .text:
            return ["Headings", "Body Text", "Labels", "Captions", "Section Headers"]
        case .layout:
            return ["Container", "Grid", "Section", "Horizontal Scroll", "Spacers", "Dividers"]
        }
    }
}

// MARK: - Design System Info
struct DSInfo {
    static let version = "1.0.0"
    static let lastUpdated = "2025-08-31"
    static let description = "Music App Design System - A comprehensive UI component library"
    
    static let principles = [
        "Consistency: Unified visual language across all components",
        "Accessibility: Components designed with accessibility in mind",
        "Scalability: Easy to extend and maintain",
        "Performance: Optimized for smooth user experience",
        "Dark Theme: Designed primarily for dark mode interfaces"
    ]
    
    static let colorPalette = [
        ("Primary", DesignTokens.Colors.primary),
        ("Background", DesignTokens.Colors.background),
        ("Surface", DesignTokens.Colors.surface),
        ("On Background", DesignTokens.Colors.onBackground),
        ("On Surface", DesignTokens.Colors.onSurface),
        ("Secondary", DesignTokens.Colors.secondary),
        ("Content Blue", DesignTokens.Colors.contentBlue),
        ("Content Orange", DesignTokens.Colors.contentOrange),
        ("Content Pink", DesignTokens.Colors.contentPink),
        ("Content Yellow", DesignTokens.Colors.contentYellow),
        ("Content Brown", DesignTokens.Colors.contentBrown),
        ("Content Gray", DesignTokens.Colors.contentGray)
    ]
}
