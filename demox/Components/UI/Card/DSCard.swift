//
//  DSCard.swift
//  demox
//
//  Created by Design System on 2025/8/31.
//

import SwiftUI

// MARK: - Card Types
enum DSCardType {
    case content
    case album
    case podcast
}

// MARK: - DS Card Component
struct DSCard: View {
    let type: DSCardType
    let title: String
    let subtitle: String?
    let description: String?
    let imageName: String?
    let backgroundColor: Color
    let action: (() -> Void)?
    
    init(
        type: DSCardType = .content,
        title: String,
        subtitle: String? = nil,
        description: String? = nil,
        imageName: String? = nil,
        backgroundColor: Color = DesignTokens.Colors.surface,
        action: (() -> Void)? = nil
    ) {
        self.type = type
        self.title = title
        self.subtitle = subtitle
        self.description = description
        self.imageName = imageName
        self.backgroundColor = backgroundColor
        self.action = action
    }
    
    var body: some View {
        Group {
            switch type {
            case .content:
                contentCard
            case .album:
                albumCard
            case .podcast:
                podcastCard
            }
        }
        .onTapGesture {
            action?()
        }
    }
    
    // MARK: - Content Card
    private var contentCard: some View {
        VStack(alignment: .leading, spacing: DesignTokens.Spacing.sm) {
            HStack {
                if let imageName = imageName {
                    DSIcon(
                        name: imageName,
                        size: .large,
                        color: DesignTokens.Colors.onSurface
                    )
                }
                Spacer()
            }
            
            Text(title)
                .font(DesignTokens.Typography.labelMedium)
                .foregroundColor(DesignTokens.Colors.onSurface)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
        }
        .padding(DesignTokens.Spacing.cardPadding)
        .frame(height: DesignTokens.Dimensions.cardHeight)
        .background(
            RoundedRectangle(cornerRadius: DesignTokens.CornerRadius.card)
                .fill(backgroundColor.opacity(0.8))
        )
    }
    
    // MARK: - Album Card
    private var albumCard: some View {
        VStack(alignment: .leading, spacing: DesignTokens.Spacing.sm) {
            RoundedRectangle(cornerRadius: DesignTokens.CornerRadius.card)
                .fill(backgroundColor)
                .frame(
                    width: DesignTokens.Dimensions.albumCardSize,
                    height: DesignTokens.Dimensions.albumCardSize
                )
                .overlay(
                    Group {
                        if let imageName = imageName {
                            DSIcon(
                                name: imageName,
                                size: .xLarge,
                                color: DesignTokens.Colors.onSurface
                            )
                        }
                    }
                )
            
            Text(title)
                .font(DesignTokens.Typography.labelMedium)
                .foregroundColor(DesignTokens.Colors.onBackground)
                .lineLimit(2)
        }
    }
    
    // MARK: - Podcast Card
    private var podcastCard: some View {
        HStack(spacing: DesignTokens.Spacing.md) {
            // Podcast image
            RoundedRectangle(cornerRadius: DesignTokens.CornerRadius.card)
                .fill(backgroundColor)
                .frame(
                    width: DesignTokens.Dimensions.podcastImageSize,
                    height: DesignTokens.Dimensions.podcastImageSize
                )
                .overlay(
                    VStack {
                        if let imageName = imageName {
                            DSIcon(
                                name: imageName,
                                size: .xLarge,
                                color: DesignTokens.Colors.onSurface
                            )
                        }
                        if let subtitle = subtitle {
                            Text(subtitle)
                                .font(DesignTokens.Typography.labelSmall)
                                .foregroundColor(DesignTokens.Colors.onSurface)
                                .multilineTextAlignment(.center)
                        }
                    }
                )
            
            VStack(alignment: .leading, spacing: DesignTokens.Spacing.sm) {
                if let description = description {
                    Text(description)
                        .font(DesignTokens.Typography.caption1)
                        .foregroundColor(DesignTokens.Colors.onSurfaceVariant)
                }
                
                Text(title)
                    .font(DesignTokens.Typography.labelLarge)
                    .foregroundColor(DesignTokens.Colors.onBackground)
                
                if let subtitle = subtitle, description != nil {
                    Text(subtitle)
                        .font(DesignTokens.Typography.bodySmall)
                        .foregroundColor(DesignTokens.Colors.onSurfaceVariant)
                        .lineLimit(3)
                }
                
                Spacer()
            }
            
            Spacer()
            
            Button(action: {
                // More action
            }) {
                DSIcon(
                    name: "ellipsis",
                    size: .medium,
                    color: DesignTokens.Colors.onSurfaceVariant
                )
            }
        }
        .padding(.horizontal, DesignTokens.Spacing.contentPadding)
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        DSCard(
            type: .content,
            title: "Brat and it's completely different",
            imageName: "music.note",
            backgroundColor: DesignTokens.Colors.contentBlue
        )
        
        DSCard(
            type: .album,
            title: "DISCLOSURE PA SALTED",
            imageName: "music.note",
            backgroundColor: DesignTokens.Colors.contentYellow
        )
        
        DSCard(
            type: .podcast,
            title: "Sounds Like A Cult",
            subtitle: "Sounds\nLike A\nCult",
            description: "Podcast",
            imageName: "mouth.fill",
            backgroundColor: DesignTokens.Colors.contentPink
        )
    }
    .padding()
    .background(DesignTokens.Colors.background)
}
