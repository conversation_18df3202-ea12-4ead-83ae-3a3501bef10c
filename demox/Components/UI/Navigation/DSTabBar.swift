//
//  DSTabBar.swift
//  demox
//
//  Created by Design System on 2025/8/31.
//

import SwiftUI

// MARK: - Tab Item Model
struct DSTabItem {
    let title: String
    let iconName: String
    let id: String
    
    init(title: String, iconName: String, id: String? = nil) {
        self.title = title
        self.iconName = iconName
        self.id = id ?? title
    }
}

// MARK: - Tab Bar Styles
enum DSTabBarStyle {
    case top
    case bottom
}

// MARK: - DS Tab Bar Component
struct DSTabBar: View {
    let items: [DSTabItem]
    let style: DSTabBarStyle
    @Binding var selectedTab: String
    
    init(
        items: [DSTabItem],
        style: DSTabBarStyle = .top,
        selectedTab: Binding<String>
    ) {
        self.items = items
        self.style = style
        self._selectedTab = selectedTab
    }
    
    var body: some View {
        Group {
            switch style {
            case .top:
                topTabBar
            case .bottom:
                bottomTabBar
            }
        }
    }
    
    // MARK: - Top Tab Bar
    private var topTabBar: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: DesignTokens.Spacing.md) {
                ForEach(items, id: \.id) { item in
                    DSButton(
                        item.title,
                        style: .tab,
                        isSelected: selectedTab == item.id
                    ) {
                        selectedTab = item.id
                    }
                }
            }
            .padding(.horizontal, DesignTokens.Spacing.contentPadding)
        }
        .padding(.top, DesignTokens.Spacing.sm)
    }
    
    // MARK: - Bottom Tab Bar
    private var bottomTabBar: some View {
        HStack {
            ForEach(items, id: \.id) { item in
                Button(action: {
                    selectedTab = item.id
                }) {
                    VStack(spacing: DesignTokens.Spacing.xs) {
                        DSIcon(
                            name: item.iconName,
                            size: .medium,
                            color: selectedTab == item.id ? 
                                DesignTokens.Colors.onBackground : 
                                DesignTokens.Colors.onSurfaceVariant
                        )
                        
                        Text(item.title)
                            .font(DesignTokens.Typography.caption2)
                            .foregroundColor(
                                selectedTab == item.id ? 
                                    DesignTokens.Colors.onBackground : 
                                    DesignTokens.Colors.onSurfaceVariant
                            )
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal, DesignTokens.Spacing.contentPadding)
        .padding(.vertical, DesignTokens.Spacing.md)
        .background(
            DesignTokens.Colors.background.opacity(0.9)
                .blur(radius: 10)
        )
    }
}

// MARK: - Convenience Initializers
extension DSTabBar {
    static func topTabs(
        tabs: [String],
        selectedTab: Binding<String>
    ) -> DSTabBar {
        let items = tabs.map { DSTabItem(title: $0, iconName: "") }
        return DSTabBar(items: items, style: .top, selectedTab: selectedTab)
    }
    
    static func bottomNavigation(
        selectedTab: Binding<String>
    ) -> DSTabBar {
        let items = [
            DSTabItem(title: "Home", iconName: "house.fill"),
            DSTabItem(title: "Search", iconName: "magnifyingglass"),
            DSTabItem(title: "Your Library", iconName: "books.vertical.fill"),
            DSTabItem(title: "Create", iconName: "plus")
        ]
        return DSTabBar(items: items, style: .bottom, selectedTab: selectedTab)
    }
}

// MARK: - Preview
#Preview {
    VStack {
        // Top Tab Bar
        DSTabBar.topTabs(
            tabs: ["All", "Music", "Podcasts", "Audiobooks"],
            selectedTab: .constant("All")
        )
        
        Spacer()
        
        // Bottom Tab Bar
        DSTabBar.bottomNavigation(
            selectedTab: .constant("Home")
        )
    }
    .background(DesignTokens.Colors.background)
}
