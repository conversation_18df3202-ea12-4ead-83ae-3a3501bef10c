//
//  DSText.swift
//  demox
//
//  Created by Design System on 2025/8/31.
//

import SwiftUI

// MARK: - Text Styles
enum DSTextStyle {
    case h1
    case h2
    case h3
    case bodyLarge
    case bodyMedium
    case bodySmall
    case labelLarge
    case labelMedium
    case labelSmall
    case caption
    
    var font: Font {
        switch self {
        case .h1:
            return DesignTokens.Typography.h1
        case .h2:
            return DesignTokens.Typography.h2
        case .h3:
            return DesignTokens.Typography.h3
        case .bodyLarge:
            return DesignTokens.Typography.bodyLarge
        case .bodyMedium:
            return DesignTokens.Typography.bodyMedium
        case .bodySmall:
            return DesignTokens.Typography.bodySmall
        case .labelLarge:
            return DesignTokens.Typography.labelLarge
        case .labelMedium:
            return DesignTokens.Typography.labelMedium
        case .labelSmall:
            return DesignTokens.Typography.labelSmall
        case .caption:
            return Font.system(size: DesignTokens.Typography.caption1)
        }
    }
}

// MARK: - Text Colors
enum DSTextColor {
    case primary
    case secondary
    case onBackground
    case onSurface
    case onSurfaceVariant
    case accent
    case error
    case warning
    case success
    
    var color: Color {
        switch self {
        case .primary:
            return DesignTokens.Colors.primary
        case .secondary:
            return DesignTokens.Colors.secondary
        case .onBackground:
            return DesignTokens.Colors.onBackground
        case .onSurface:
            return DesignTokens.Colors.onSurface
        case .onSurfaceVariant:
            return DesignTokens.Colors.onSurfaceVariant
        case .accent:
            return DesignTokens.Colors.accent
        case .error:
            return DesignTokens.Colors.error
        case .warning:
            return DesignTokens.Colors.warning
        case .success:
            return DesignTokens.Colors.success
        }
    }
}

// MARK: - DS Text Component
struct DSText: View {
    let text: String
    let style: DSTextStyle
    let color: DSTextColor
    let lineLimit: Int?
    let alignment: TextAlignment
    
    init(
        _ text: String,
        style: DSTextStyle = .bodyMedium,
        color: DSTextColor = .onBackground,
        lineLimit: Int? = nil,
        alignment: TextAlignment = .leading
    ) {
        self.text = text
        self.style = style
        self.color = color
        self.lineLimit = lineLimit
        self.alignment = alignment
    }
    
    var body: some View {
        Text(text)
            .font(style.font)
            .foregroundColor(color.color)
            .lineLimit(lineLimit)
            .multilineTextAlignment(alignment)
    }
}

// MARK: - Convenience Initializers
extension DSText {
    static func heading1(
        _ text: String,
        color: DSTextColor = .onBackground
    ) -> DSText {
        DSText(text, style: .h1, color: color)
    }
    
    static func heading2(
        _ text: String,
        color: DSTextColor = .onBackground
    ) -> DSText {
        DSText(text, style: .h2, color: color)
    }
    
    static func heading3(
        _ text: String,
        color: DSTextColor = .onBackground
    ) -> DSText {
        DSText(text, style: .h3, color: color)
    }
    
    static func body(
        _ text: String,
        color: DSTextColor = .onBackground,
        lineLimit: Int? = nil
    ) -> DSText {
        DSText(text, style: .bodyMedium, color: color, lineLimit: lineLimit)
    }
    
    static func label(
        _ text: String,
        color: DSTextColor = .onBackground,
        size: DSTextStyle = .labelMedium
    ) -> DSText {
        DSText(text, style: size, color: color)
    }
    
    static func caption(
        _ text: String,
        color: DSTextColor = .onSurfaceVariant
    ) -> DSText {
        DSText(text, style: .caption, color: color)
    }
}



// MARK: - Preview
#Preview {
    VStack(alignment: .leading, spacing: 20) {
        DSText.heading1("Heading 1")
        DSText.heading2("Heading 2")
        DSText.heading3("Heading 3")
        DSText.body("This is body text that can span multiple lines and provides good readability.")
        DSText.label("Label Text")
        DSText.caption("Caption text for additional information")
        
        Divider()

        // Section Header Example
        HStack {
            VStack(alignment: .leading, spacing: DesignTokens.Spacing.xs) {
                DSText.heading2("Picked for you")
                DSText.caption("Based on your listening history")
            }
            Spacer()
            Button(action: {
                print("See all tapped")
            }) {
                DSText.label("See All", color: .accent, size: .labelSmall)
            }
        }
    }
    .padding()
    .background(DesignTokens.Colors.background)
}
