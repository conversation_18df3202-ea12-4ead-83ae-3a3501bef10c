//
//  DSPlayground.swift
//  demox
//
//  Created by Design System on 2025/8/31.
//

import SwiftUI

// MARK: - Design System Playground
struct DSPlayground: View {
    @State private var selectedCategory: DSComponentCategory = .tokens
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Category Selector
                categorySelector
                
                // Content
                ScrollView {
                    LazyVStack(spacing: DesignTokens.Spacing.xl) {
                        playgroundContent
                    }
                    .padding(.bottom, 100)
                }
            }
            .background(DesignTokens.Colors.background.ignoresSafeArea())
            .navigationTitle("Design System")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    // MARK: - Category Selector
    private var categorySelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: DesignTokens.Spacing.md) {
                ForEach(DSComponentCategory.allCases, id: \.self) { category in
                    DSButton(
                        category.rawValue,
                        style: .tab,
                        size: .small,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal, DesignTokens.Spacing.contentPadding)
        }
        .padding(.vertical, DesignTokens.Spacing.sm)
    }
    
    // MARK: - Playground Content
    @ViewBuilder
    private var playgroundContent: some View {
        switch selectedCategory {
        case .tokens:
            tokensPlayground
        case .buttons:
            buttonsPlayground
        case .cards:
            cardsPlayground
        case .icons:
            iconsPlayground
        case .navigation:
            navigationPlayground
        case .text:
            textPlayground
        case .layout:
            layoutPlayground
        }
    }
    
    // MARK: - Tokens Playground
    private var tokensPlayground: some View {
        VStack(spacing: DesignTokens.Spacing.xl) {
            // Colors
            DSSection(title: "Color Palette") {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: DesignTokens.Spacing.md) {
                    ForEach(DSInfo.colorPalette, id: \.0) { name, color in
                        VStack(spacing: DesignTokens.Spacing.sm) {
                            RoundedRectangle(cornerRadius: DesignTokens.CornerRadius.card)
                                .fill(color)
                                .frame(height: 60)
                            
                            DSText.caption(name, color: .onSurfaceVariant)
                        }
                    }
                }
                .padding(.horizontal, DesignTokens.Spacing.contentPadding)
            }
            
            // Typography
            DSSection(title: "Typography") {
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading1("Heading 1")
                    DSText.heading2("Heading 2")
                    DSText.heading3("Heading 3")
                    DSText.body("Body text for content and descriptions")
                    DSText.label("Label text for UI elements")
                    DSText.caption("Caption text for additional info")
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, DesignTokens.Spacing.contentPadding)
            }
            
            // Spacing
            DSSection(title: "Spacing Scale") {
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.sm) {
                    spacingExample("XS", DesignTokens.Spacing.xs)
                    spacingExample("SM", DesignTokens.Spacing.sm)
                    spacingExample("MD", DesignTokens.Spacing.md)
                    spacingExample("LG", DesignTokens.Spacing.lg)
                    spacingExample("XL", DesignTokens.Spacing.xl)
                    spacingExample("XXL", DesignTokens.Spacing.xxl)
                    spacingExample("XXXL", DesignTokens.Spacing.xxxl)
                }
                .padding(.horizontal, DesignTokens.Spacing.contentPadding)
            }
        }
    }
    
    private func spacingExample(_ name: String, _ value: CGFloat) -> some View {
        HStack {
            DSText.label(name, size: .labelSmall)
                .frame(width: 40, alignment: .leading)
            
            Rectangle()
                .fill(DesignTokens.Colors.primary)
                .frame(width: value, height: 20)
            
            DSText.caption("\(Int(value))pt")
            
            Spacer()
        }
    }
    
    // MARK: - Buttons Playground
    private var buttonsPlayground: some View {
        DSSection(title: "Button Variants") {
            VStack(spacing: DesignTokens.Spacing.lg) {
                // Button Styles
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Styles")
                    
                    HStack(spacing: DesignTokens.Spacing.md) {
                        DSButton("Primary", style: .primary) {}
                        DSButton("Secondary", style: .secondary) {}
                        DSButton("Ghost", style: .ghost) {}
                    }
                    
                    HStack(spacing: DesignTokens.Spacing.md) {
                        DSButton("Tab", style: .tab) {}
                        DSButton("Selected", style: .tab, isSelected: true) {}
                    }
                }
                
                // Button Sizes
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Sizes")
                    
                    VStack(spacing: DesignTokens.Spacing.sm) {
                        DSButton("Large Button", style: .primary, size: .large) {}
                        DSButton("Medium Button", style: .primary, size: .medium) {}
                        DSButton("Small Button", style: .primary, size: .small) {}
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, DesignTokens.Spacing.contentPadding)
        }
    }

    // MARK: - Cards Playground
    private var cardsPlayground: some View {
        DSSection(title: "Card Types") {
            VStack(spacing: DesignTokens.Spacing.xl) {
                // Content Cards
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Content Cards")

                    DSGrid(columns: 2) {
                        DSCard(
                            type: .content,
                            title: "Brat and it's completely different",
                            imageName: "music.note",
                            backgroundColor: DesignTokens.Colors.contentBlue
                        )

                        DSCard(
                            type: .content,
                            title: "Wicked Official Playlist",
                            imageName: "music.note",
                            backgroundColor: DesignTokens.Colors.contentOrange
                        )

                        DSCard(
                            type: .content,
                            title: "Gracie Abrams",
                            imageName: "person.circle.fill",
                            backgroundColor: DesignTokens.Colors.contentPink
                        )

                        DSCard(
                            type: .content,
                            title: "Today's Top Hits",
                            imageName: "music.note",
                            backgroundColor: DesignTokens.Colors.contentGray
                        )
                    }
                }
                .padding(.horizontal, DesignTokens.Spacing.contentPadding)

                // Album Cards
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Album Cards")

                    DSHorizontalScroll {
                        ForEach(0..<3, id: \.self) { index in
                            DSCard(
                                type: .album,
                                title: "Album \(index + 1)",
                                imageName: "music.note",
                                backgroundColor: [DesignTokens.Colors.contentYellow, DesignTokens.Colors.contentBlue, DesignTokens.Colors.contentBrown][index]
                            )
                        }
                    }
                }

                // Podcast Card
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Podcast Card")

                    DSCard(
                        type: .podcast,
                        title: "Sounds Like A Cult",
                        subtitle: "A podcast about the modern-day \"cults\" we all follow.",
                        description: "Podcast",
                        imageName: "mouth.fill",
                        backgroundColor: DesignTokens.Colors.contentPink
                    )
                }
                .padding(.horizontal, DesignTokens.Spacing.contentPadding)
            }
        }
    }

    // MARK: - Icons Playground
    private var iconsPlayground: some View {
        DSSection(title: "Icon Library") {
            VStack(spacing: DesignTokens.Spacing.xl) {
                // Icon Sizes
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Icon Sizes")

                    HStack(spacing: DesignTokens.Spacing.xl) {
                        VStack(spacing: DesignTokens.Spacing.sm) {
                            DSIcon.music(size: .small)
                            DSText.caption("Small")
                        }

                        VStack(spacing: DesignTokens.Spacing.sm) {
                            DSIcon.music(size: .medium)
                            DSText.caption("Medium")
                        }

                        VStack(spacing: DesignTokens.Spacing.sm) {
                            DSIcon.music(size: .large)
                            DSText.caption("Large")
                        }

                        VStack(spacing: DesignTokens.Spacing.sm) {
                            DSIcon.music(size: .xLarge)
                            DSText.caption("X-Large")
                        }
                    }
                }

                // Music Icons
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Music Icons")

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: DesignTokens.Spacing.lg) {
                        iconExample(DSIcon.music(), "Music")
                        iconExample(DSIcon.person(), "Person")
                        iconExample(DSIcon.waveform(), "Waveform")
                        iconExample(DSIcon.sun(), "Sun")
                        iconExample(DSIcon.heart(), "Heart")
                        iconExample(DSIcon.more(), "More")
                    }
                }

                // Navigation Icons
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Navigation Icons")

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: DesignTokens.Spacing.lg) {
                        iconExample(DSIcon.home(), "Home")
                        iconExample(DSIcon.search(), "Search")
                        iconExample(DSIcon.library(), "Library")
                        iconExample(DSIcon.create(), "Create")
                    }
                }
            }
            .padding(.horizontal, DesignTokens.Spacing.contentPadding)
        }
    }

    private func iconExample(_ icon: DSIcon, _ name: String) -> some View {
        VStack(spacing: DesignTokens.Spacing.sm) {
            icon
            DSText.caption(name)
        }
    }

    // MARK: - Navigation Playground
    private var navigationPlayground: some View {
        DSSection(title: "Navigation Components") {
            VStack(spacing: DesignTokens.Spacing.xl) {
                // Top Tab Bar
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Top Tab Bar")

                    DSTabBar.topTabs(
                        tabs: ["All", "Music", "Podcasts", "Audiobooks"],
                        selectedTab: .constant("All")
                    )
                }

                // Bottom Navigation
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Bottom Navigation")

                    DSTabBar.bottomNavigation(
                        selectedTab: .constant("Home")
                    )
                }
            }
        }
    }

    // MARK: - Text Playground
    private var textPlayground: some View {
        DSSection(title: "Text & Typography") {
            VStack(alignment: .leading, spacing: DesignTokens.Spacing.xl) {
                // Text Styles
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Text Styles")

                    VStack(alignment: .leading, spacing: DesignTokens.Spacing.sm) {
                        DSText.heading1("Heading 1 - Large Title")
                        DSText.heading2("Heading 2 - Section Title")
                        DSText.heading3("Heading 3 - Subsection")
                        DSText.body("Body text for content and descriptions that can span multiple lines.")
                        DSText.label("Label text for UI elements")
                        DSText.caption("Caption text for additional information")
                    }
                }

                // Text Colors
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Text Colors")

                    VStack(alignment: .leading, spacing: DesignTokens.Spacing.sm) {
                        DSText.body("Primary Text", color: .primary)
                        DSText.body("Secondary Text", color: .secondary)
                        DSText.body("On Background", color: .onBackground)
                        DSText.body("On Surface Variant", color: .onSurfaceVariant)
                        DSText.body("Accent Text", color: .accent)
                        DSText.body("Error Text", color: .error)
                        DSText.body("Warning Text", color: .warning)
                        DSText.body("Success Text", color: .success)
                    }
                }

                // Section Headers
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Section Headers")

                    VStack(spacing: DesignTokens.Spacing.lg) {
                        DSSectionHeader("Picked for you")
                        DSSectionHeader("New releases for you", subtitle: "Based on your listening history") {}
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, DesignTokens.Spacing.contentPadding)
        }
    }

    // MARK: - Layout Playground
    private var layoutPlayground: some View {
        DSSection(title: "Layout Components") {
            VStack(spacing: DesignTokens.Spacing.xl) {
                // Grid Layout
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Grid Layout")

                    DSGrid(columns: 3) {
                        ForEach(0..<6, id: \.self) { index in
                            RoundedRectangle(cornerRadius: DesignTokens.CornerRadius.card)
                                .fill(DesignTokens.Colors.primary)
                                .frame(height: 60)
                                .overlay(
                                    DSText.label("\(index + 1)", color: .onBackground)
                                )
                        }
                    }
                    .padding(.horizontal, DesignTokens.Spacing.contentPadding)
                }

                // Horizontal Scroll
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Horizontal Scroll")

                    DSHorizontalScroll {
                        ForEach(0..<5, id: \.self) { index in
                            RoundedRectangle(cornerRadius: DesignTokens.CornerRadius.card)
                                .fill(DesignTokens.Colors.contentBlue)
                                .frame(width: 100, height: 80)
                                .overlay(
                                    DSText.label("Item \(index + 1)", color: .onBackground)
                                )
                        }
                    }
                }

                // Spacers and Dividers
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.md) {
                    DSText.heading3("Spacers & Dividers")

                    VStack(spacing: 0) {
                        DSText.body("Content above divider")
                        DSSpacer(height: DesignTokens.Spacing.lg)
                        DSDivider()
                        DSSpacer(height: DesignTokens.Spacing.lg)
                        DSText.body("Content below divider")
                    }
                }
                .padding(.horizontal, DesignTokens.Spacing.contentPadding)
            }
        }
    }
}

// MARK: - Preview
#Preview {
    DSPlayground()
}
